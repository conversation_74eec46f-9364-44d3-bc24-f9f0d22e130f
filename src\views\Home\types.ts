export type WorkplaceTotal = {
  project: number
  access: number
  todo: number
}

export type Project = {
  name: string
  icon: string
  message: string
  personal: string
  time: Date | number | string
  color: string
}

export type Notice = {
  title: string
  type: string
  keys: string[]
  date: Date | number | string
}

export type Shortcut = {
  name: string
  icon: string
  url: string
  color: string
}

export type RadarData = {
  personal: number
  team: number
  max: number
  name: string
}
export type AnalysisTotalTypes = {
  users: number
  messages: number
  moneys: number
  shoppings: number
}

export type UserAccessSource = {
  value: number
  name: string
}

export type WeeklyUserActivity = {
  value: number
  name: string
}

export type MonthlySales = {
  name: string
  estimate: number
  actual: number
}

// 租车系统类型定义

// 系统概览数据类型
export type SystemOverview = {
  totalOrders: number
  totalVehicles: number
  activeStores: number
  monthlyRevenue: number
}

// 车辆状态数据类型
export type VehicleStatusItem = {
  value: number
  name: string
}

// 车辆品牌数据类型
export type VehicleBrandItem = {
  value: number
  name: string
}

// 热门车型数据类型
export type PopularVehicle = {
  name: string
  count: number
  image: string
}

// 电池续航里程排行数据类型
export type BatteryRangeItem = {
  name: string
  range: number
  batteryHealth: number
  image: string
}

// 订单趋势数据类型
export type OrderTrendData = {
  dates: string[]
  rentOrders: number[]
  purchaseOrders: number[]
}

// 待处理订单数据类型
export type PendingOrder = {
  id: string
  customerName: string
  type: string
  vehicle: string
  status: string
  time: string
}

// 订单状态数据类型
export type OrderStatusItem = {
  value: number
  name: string
}

// 门店业绩数据类型
export type StorePerformance = {
  name: string
  revenue: number
  vehicleCount: number
}

// 门店地理分布数据类型
export type StoreLocation = {
  name: string
  value: number
}

// 快捷功能入口数据类型
export type QuickAccessLink = {
  name: string
  icon: string
  url: string
  color: string
}

// 租赁周期数据类型
export type RentalCycleItem = {
  value: number
  name: string
}

// 收入构成数据类型
export type RevenueCompositionItem = {
  value: number
  name: string
}

// 用户活跃度数据类型
export type UserActivityData = {
  dates: string[]
  newUsers: number[]
  activeUsers: number[]
}

// 系统通知数据类型
export type SystemNotice = {
  title: string
  content: string
  date: string
  type: string
  status: string
}

// 车辆维护状态数据类型
export type MaintenanceItem = {
  vehicleNo: string
  name: string
  lastMaintenance: string
  status: string
  batteryLevel: number
}
