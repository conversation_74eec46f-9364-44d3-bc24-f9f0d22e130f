<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="图片地址" prop="imageUrl">
        <UploadImg v-model="formData.imageUrl" />
      </el-form-item>
      <el-form-item label="跳转类型" prop="linkType">
        <el-select v-model="formData.linkType" placeholder="请选择跳转类型">
          <el-option
            v-for="dict in linkTypeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="跳转内容" prop="linkValue" v-if="formData.linkType !== 'none'">
        <el-input v-model="formData.linkValue" placeholder="请输入跳转内容" />

      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" :max="9999" />
      </el-form-item>
      <el-form-item label="是否启用" prop="enabled">
        <el-select v-model="formData.enabled" placeholder="请选择是否启用">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.RENTAL_STORE_ENABLED)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { HomeBannerApi, HomeBannerVO } from '@/api/rental/banner'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 轮播图 表单 */
defineOptions({ name: 'HomeBannerForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  title: undefined,
  imageUrl: undefined,
  linkType: undefined,
  linkValue: undefined,
  sort: undefined,
  enabled: undefined,
  remark: undefined,
})

// 跳转类型（none=不跳转，url=外链，page=小程序页面）
const linkTypeOptions = ref([
  { value: 'none', label: '不跳转' },
  { value: 'url', label: '外链' },
  { value: 'page', label: '小程序页面' },
])


const formRules = reactive({
  title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
  imageUrl: [{ required: true, message: '图片地址不能为空', trigger: 'blur' }],
  linkType: [{ required: true, message: '跳转类型（none=不跳转，url=外链，page=小程序页面）不能为空', trigger: 'change' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  enabled: [{ required: true, message: '是否启用（1=启用，0=禁用）不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await HomeBannerApi.getHomeBanner(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as HomeBannerVO
    if (formType.value === 'create') {
      await HomeBannerApi.createHomeBanner(data)
      message.success(t('common.createSuccess'))
    } else {
      await HomeBannerApi.updateHomeBanner(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: undefined,
    imageUrl: undefined,
    linkType: undefined,
    linkValue: undefined,
    sort: undefined,
    enabled: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}
</script>
