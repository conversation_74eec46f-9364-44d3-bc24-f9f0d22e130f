<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="门店名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入门店名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="门店状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择门店状态"
          clearable
          class="!w-240px"
        >
                  <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.RENTAL_STORE_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" type="index" align="center" width="80" />
      <el-table-column label="门店名称" align="center" prop="name" />
      <el-table-column label="门店地址" align="center" prop="address" />
      <el-table-column label="经度" align="center" prop="longitude" />
      <el-table-column label="纬度" align="center" prop="latitude" />
      <el-table-column label="营业时间" align="center" prop="businessHours" />
      <el-table-column label="到店取车" align="center" prop="supportPickup">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.RENTAL_STORE_SUPPORT_PICKUP" :value="scope.row.supportPickup" />
        </template>
      </el-table-column>
      <el-table-column label="送车上门" align="center" prop="supportDelivery">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.RENTAL_STORE_SUPPORT_DELIVERY" :value="scope.row.supportDelivery" />
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="门店状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.RENTAL_STORE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="160px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="primary"
            @click="openQrCodeDialog(scope.row.id)"
          >
            二维码
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleBlock(scope.row.id)"
          >
            黑名单
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <StoreForm ref="formRef" @success="getList" />
  
  <!-- 二维码弹窗 -->
  <StoreQrCodeDialog ref="qrCodeDialogRef" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { StoreApi, StoreVO } from '@/api/rental/store'
import StoreForm from './StoreForm.vue'
import StoreQrCodeDialog from './StoreQrCodeDialog.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 租车门店 列表 */
defineOptions({ name: 'Store' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<StoreVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  address: undefined,
  longitude: undefined,
  latitude: undefined,
  businessHours: undefined,
  supportPickup: undefined,
  supportDelivery: undefined,
  phone: undefined,
  status: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await StoreApi.getStorePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await StoreApi.deleteStore(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await StoreApi.exportStore(queryParams)
    download.excel(data, '租车门店.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 打开二维码弹窗 */
const qrCodeDialogRef = ref()
const openQrCodeDialog = (id: number) => {
  qrCodeDialogRef.value.open(id)
}
const { push } = useRouter()
/**拉黑 */
const handleBlock = (id: number) => {
  // 打开详情页面
  push({ name: 'RentalStoreblackList', params: { id } })
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
