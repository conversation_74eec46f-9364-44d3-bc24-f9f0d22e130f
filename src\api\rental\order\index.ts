import request from '@/config/axios'

// 租车/购车统一订单 VO
export interface OrderVO {
  id: number // 主键ID
  orderNo: string // 订单编号
  userId: number // 用户ID
  orderType: string // 订单类型：rent=租车，buy=购车
  storeId: number // 门店ID
  vehicleId: number // 车辆ID
  depositType: string // 押金类型：normal=交押金，alipay_free=支付宝免押
  depositAmount: number // 押金金额（仅租车）
  rentMode: string // 租赁周期：day/week/month/quarter
  rentPaymentMode: string // 支付方式：prepay=先付后用，postpay=先用后付
  rentStartTime: Date // 租赁开始时间
  rentDuration: number // 租赁时长
  purchasePlan: string // 分期方案：full, 3, 6, 12
  installmentFee: number // 分期手续费
  amount: number // 订单金额（不含押金）
  payType: string // 支付方式
  alipayTradeNo: string // 支付宝交易凭证号
  status: string // 订单状态
  isDeleted: boolean // 是否逻辑删除
  storeName: string // 门店名称
  vehicleName: string // 租车名称
  vehicleVin: string // 车架号
  licensePlate: string // 车牌
  vehicleImage: string // 车辆图片
  userName: string // 用户名
  userPhone: string // 用户手机
  pickupMethod: string // 取车方式：到店取车/送车上门(to_store,deliver)
  addressId: number // 地址ID
  fullAddress: string // 收车地址
  createTime: number // 创建时间
  cancelTime: number // 取消时间
  payTime: number // 支付时间
  finishTime: number // 完成时间
  refundTime: number // 退款时间
  refundAmount: number // 退款金额
  refundReason: string // 退款原因
}

// 租车/购车统一订单 API
export const OrderApi = {
  // 查询租车/购车统一订单分页
  getOrderPage: async (params: any) => {
    return await request.get({ url: `/rental/order/page`, params })
  },

  // 查询租车/购车统一订单详情
  getOrder: async (id: number) => {
    return await request.get({ url: `/rental/order/get?id=` + id })
  },

  // 新增租车/购车统一订单
  createOrder: async (data: OrderVO) => {
    return await request.post({ url: `/rental/order/create`, data })
  },

  // 修改租车/购车统一订单
  updateOrder: async (data: OrderVO) => {
    return await request.put({ url: `/rental/order/update`, data })
  },

  // 删除租车/购车统一订单
  deleteOrder: async (id: number) => {
    return await request.delete({ url: `/rental/order/delete?id=` + id })
  },

  // 导出租车/购车统一订单 Excel
  exportOrder: async (params) => {
    return await request.download({ url: `/rental/order/export-excel`, params })
  },

  // 取消订单
  cancelOrder: async (id: number) => {
    return await request.post({ url: `/rental/order/cancel?id=` + id })
  },

  /**
   * @description 退款订单
   * @param id 订单ID
   * @param refundAmount 退款金额
   * @param refundReason 退款原因
   * @returns 
   */
  refundOrder: async (data: any) => {
    return await request.post({ url: `/rental/order/refund`, data })
  },

  // 完成订单
  finishOrder: async (orderNo: string) => {
    return await request.post({ url: `/rental/order/finish`, params: { orderNo } })
  }
}
