import request from '@/config/axios'

// 租赁车辆 VO
export interface VehicleRentVO {
  id: number // 主键ID
  vehicleNo: string // 车辆编号（平台内部唯一标识）
  licensePlate: string // 车牌号
  vin: string // 车架号（VIN）
  storeId: number // 所属门店ID（逻辑关联 rental_store.id）
  brandId: number // 品牌ID
  model: string // 型号
  detail: string // 车辆详细说明（如颜色、配置等）
  price: number // 租赁价格（单位：元/天）
  images: string // 车辆图片，JSON数组格式（["url1", "url2", ...]）
  name: string // 名称
  status: number // 状态（1=上架，2=下架）
  deposit: number // 押金
}

// 租赁车辆 API
export const VehicleRentApi = {
  // 查询租赁车辆分页
  getVehicleRentPage: async (params: any) => {
    return await request.get({ url: `/rental/vehicle-rent/page`, params })
  },

  // 查询租赁车辆详情
  getVehicleRent: async (id: number) => {
    return await request.get({ url: `/rental/vehicle-rent/get?id=` + id })
  },

  // 新增租赁车辆
  createVehicleRent: async (data: VehicleRentVO) => {
    return await request.post({ url: `/rental/vehicle-rent/create`, data })
  },

  // 修改租赁车辆
  updateVehicleRent: async (data: VehicleRentVO) => {
    return await request.put({ url: `/rental/vehicle-rent/update`, data })
  },

  // 删除租赁车辆
  deleteVehicleRent: async (id: number) => {
    return await request.delete({ url: `/rental/vehicle-rent/delete?id=` + id })
  },

  // 导出租赁车辆 Excel
  exportVehicleRent: async (params) => {
    return await request.download({ url: `/rental/vehicle-rent/export-excel`, params })
  },

  // 划拨
  transferVehicleRent: async (id: number, storeId: number) => {
    return await request.get({ url: `/rental/vehicle-rent/transfer?id=${id}&storeId=${storeId}` })
  }

}
