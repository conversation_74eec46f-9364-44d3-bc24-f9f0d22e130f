<template>
  <ContentWrap>
    <div class="order-detail">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <h2>订单详情</h2>
          <dict-tag :type="DICT_TYPE.VEHICLE_ORDER_STATUS" :value="orderData.status" />
        </div>
        <div class="header-right">
         <!--  <el-button v-if="canChangeStatus" type="primary" @click="handleStatusChange" :loading="statusLoading">
            {{ orderData.status === 'pending' ? '支付订单' : '' }}
          </el-button> -->
          <el-button type="primary" v-if="orderData.status === 'in_progress' || orderData.status === 'paid'" @click="handleFinish">
            完成订单
          </el-button>
          <!-- 退款按钮 -->
          <el-button v-if="orderData.status === 'completed'" type="danger" @click="refundDialogVisible = true">
            订单退款
          </el-button>
          <!-- 取消按钮 -->
          <el-button v-if="orderData.status === 'pending'" type="warning" @click="handleCancel">
            取消订单
          </el-button>
          <el-button @click="goBack" :icon="ArrowLeftBold">返回</el-button>
        </div>
      </div>
      <!-- 订单信息卡片 -->
      <el-card class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>订单信息</span>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="订单编号">{{ orderData.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="订单类型">
            <el-tag :type="orderData.orderType === 'rent' ? 'success' : 'danger'">
              {{ orderData.orderType === 'rent' ? '租车' : '购车' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <dict-tag :type="DICT_TYPE.VEHICLE_ORDER_STATUS" :value="orderData.status" />
          </el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ orderData.amount }}</el-descriptions-item>
          <!-- <el-descriptions-item label="支付方式">
            <el-tag :type="orderData.payType === 'alipay' ? 'success' : 'danger'">
              {{ orderData.payType === 'alipay' ? '支付宝' : '微信' }}
            </el-tag>
          </el-descriptions-item> -->
          <el-descriptions-item label="支付方式">
            <el-tag :type="orderData.rentPaymentMode === 'prepay' ? 'success' : 'danger'">
              {{ orderData.rentPaymentMode === 'prepay' ? '先付后用' : '先用后付' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付宝交易凭证号">{{ orderData.alipayTradeNo || '--' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(orderData.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="支付时间" v-if="orderData.payTime">{{ formatDate(orderData.payTime)
          }}</el-descriptions-item>
          <el-descriptions-item label="完成时间" v-if="orderData.finishTime">{{ formatDate(orderData.finishTime)
          }}</el-descriptions-item>
          <el-descriptions-item label="取消时间" v-if="orderData.cancelTime">{{ formatDate(orderData.cancelTime)
          }}</el-descriptions-item>
          <el-descriptions-item label="退款时间" v-if="orderData.refundTime">{{ formatDate(orderData.refundTime)
          }}</el-descriptions-item>
          <el-descriptions-item label="退款金额" v-if="orderData.refundAmount">{{ orderData.refundAmount || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="退款原因" v-if="orderData.refundReason">{{ orderData.refundReason || '--'
          }}</el-descriptions-item>
           <el-descriptions-item label="下单用户">
            <el-link type="primary" @click="goMemberDetail(createUserInfo.id)">
              {{ createUserInfo.nickname }}&nbsp;（{{ createUserInfo.phoneNumber }}）
            </el-link>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 租赁信息卡片 -->
      <el-card class="detail-card" shadow="hover" v-if="orderData.orderType === 'rent'">
        <template #header>
          <div class="card-header">
            <span>租赁信息</span>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="押金类型">
            <el-tag :type="orderData.depositType === 'alipay_free' ? 'success' : 'danger'">
              {{ orderData.depositType === 'alipay_free' ? '支付宝免押' : '交押金' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="租赁周期">
            <dict-tag :type="DICT_TYPE.RENTAL_CUSTOM_PRICE_MODEL" :value="orderData.rentMode" />
          </el-descriptions-item>
          <el-descriptions-item label="租赁开始时间">{{ formatDate(orderData.rentStartTime) }}</el-descriptions-item>
          <el-descriptions-item label="租赁结束时间" v-if="orderData.rentEndTime && orderData.rentMode === 'custom'">
            {{ formatDate(orderData.rentEndTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="租赁时长">{{ orderData.rentDuration }}天</el-descriptions-item>
          <!-- 租车订单退押金 -->
          <el-descriptions-item label="押金金额">¥{{ orderData.depositAmount || '--' }}</el-descriptions-item>
          <template v-if="orderData.orderType == 'rent' && orderData.depositAmount > 0 && orderData.status == 'completed'">
            <el-descriptions-item label="押金退还状态" v-if="orderData.rentDepositRefundStatus=='Y'">已退还</el-descriptions-item>
            <el-descriptions-item label="押金退还时间" v-if="orderData.rentDepositRefundTime">{{ formatDate(orderData.rentDepositRefundTime) }}</el-descriptions-item>
          </template>
        </el-descriptions>
      </el-card>

      <!-- 分期信息卡片 -->
      <el-card class="detail-card" shadow="hover" v-if="orderData.purchasePlan && orderData.purchasePlan !== 'full'">
        <template #header>
          <div class="card-header">
            <span>分期信息</span>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="分期方案">
            <dict-tag :type="DICT_TYPE.RENTAL_PURCHASE_PLAN" :value="orderData.purchasePlan" />
          </el-descriptions-item>
          <el-descriptions-item label="分期手续费">¥{{ orderData.installmentFee || '--' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 收车信息卡片 -->
      <el-card class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>收车信息</span>
            <el-button v-if="orderData.status === 'pending'" type="primary" link @click="openAddressSelector">
              编辑
            </el-button>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="取车方式">
            <el-tag :type="orderData.pickupMethod === 'to_store' ? 'success' : 'danger'">
              {{ orderData.pickupMethod === 'to_store' ? '到店取车' : '送车上门' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="收车地址">{{ getFullAddress || '' }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ orderData.userName }}</el-descriptions-item>
          <el-descriptions-item label="用户手机">{{ orderData.userPhone }}</el-descriptions-item>
        </el-descriptions>
        <!-- 编辑表单 -->
        <AddressSelector v-model="addressInfo" ref="addressSelectorRef" @select="selectAddress" />
      </el-card>

      <!-- 车辆信息卡片 -->
      <el-card class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>车辆信息</span>
            <el-button v-if="orderData.status === 'pending' && !isEditVehicle" type="primary" link
              @click="editVehicleInfo">
              编辑
            </el-button>
            <el-button type="primary" link @click="saveVehicleInfo"
              v-if="orderData.status === 'pending' && isEditVehicle">保存</el-button>
          </div>
        </template>
        <el-descriptions :column="3" border v-if="isEditVehicle">
          <el-descriptions-item label="车辆名称">
            <el-input v-model="orderData.vehicleName" />
          </el-descriptions-item>
          <el-descriptions-item label="车架号">
            <el-input v-model="orderData.vehicleVin" />
          </el-descriptions-item>
          <el-descriptions-item label="车牌号">
            <el-input v-model="orderData.licensePlate" />
          </el-descriptions-item>
          <el-descriptions-item label="车辆图片">
            <div v-if="orderData.vehicleImage" class="vehicle-image-container">
              <el-image :src="orderData.vehicleImage" :preview-src-list="[orderData.vehicleImage]" fit="cover"
                class="vehicle-image" :preview-teleported="true" />
            </div>
            <span v-else class="no-image">暂无图片</span>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions :column="3" border v-else>
          <el-descriptions-item label="车辆名称">{{ orderData.vehicleName }}</el-descriptions-item>
          <el-descriptions-item label="车架号">{{ orderData.vehicleVin }}</el-descriptions-item>
          <el-descriptions-item label="车牌号">{{ orderData.licensePlate }}</el-descriptions-item>
          <el-descriptions-item label="车辆图片">
            <div v-if="orderData.vehicleImage" class="vehicle-image-container">
              <el-image :src="orderData.vehicleImage" :preview-src-list="[orderData.vehicleImage]" fit="cover"
                class="vehicle-image" :preview-teleported="true" />
            </div>
            <span v-else class="no-image">暂无图片</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 门店信息卡片 -->
      <el-card class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>门店信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="门店名称">{{ orderData.storeName }}</el-descriptions-item>
          <el-descriptions-item label="门店地址">{{ storeInfo.address || '--' }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ storeInfo.phone || '--' }}</el-descriptions-item>
          <el-descriptions-item label="营业时间">{{ storeInfo.businessHours || '--' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 订单操作日志卡片 -->
      <el-card class="detail-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>订单操作日志</span>
            <el-button type="primary" link @click="getOrderOperationLogs" :loading="operationLogsLoading">
              刷新
            </el-button>
          </div>
        </template>
        <div v-if="operationLogs.length > 0">
          <el-timeline>
            <el-timeline-item v-for="(log, index) in operationLogs" :key="index"
              :timestamp="formatDate(log.operationTime)" placement="top">
              <div class="timeline-content">
                <el-tag size="small" :type="getOperationUserTypeTag(log.operationUserType)" class="mr-5">
                  {{ getOperationUserTypeText(log.operationUserType) }}
                </el-tag>
                <span class="user-name">{{ log.operationUserName }}</span>
                <span class="operation-type">
                  <dict-tag :type="DICT_TYPE.RENTAL_ORDER_OPERATION_TYPE" :value="log.operationType" />
                </span>
                <template v-if="log.previousStatus && log.currentStatus">
                  <span>将订单状态从</span>
                  <dict-tag :type="DICT_TYPE.VEHICLE_ORDER_STATUS" :value="log.previousStatus" />
                  <span>修改为</span>
                  <dict-tag :type="DICT_TYPE.VEHICLE_ORDER_STATUS" :value="log.currentStatus" />
                </template>
                <!--  <div v-if="log.remark" class="remark">
                  备注：{{ log.remark }}
                </div> -->
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
        <el-empty v-else description="暂无操作日志" />
      </el-card>
    </div>
    <!-- 退款弹窗 -->
    <el-dialog v-model="refundDialogVisible" title="退款" width="30%">
      <el-form :model="refundForm" label-width="120px" :rules="refundFormRules" ref="refundFormRef">
        <el-form-item label="退款金额" prop="refundAmount">
          <el-input v-model="refundForm.refundAmount" type="number" />
        </el-form-item>
        <el-form-item label="退款原因">
          <el-input v-model="refundForm.refundReason" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="refundDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleRefund">确定</el-button>
      </template>
    </el-dialog>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ArrowLeftBold } from '@element-plus/icons-vue'
import { OrderApi, OrderVO } from '@/api/rental/order'
import { StoreApi, StoreVO } from '@/api/rental/store'
import { MemberApi, MemberVO } from '@/api/rental/member'
import { MemberAddressApi, MemberAddressVO } from '@/api/rental/memberaddress'
import { OrderOperationLogApi, OrderOperationLogVO } from '@/api/rental/orderoperationlog'
import { DICT_TYPE } from '@/utils/dict'

/** 订单详情页面 */
defineOptions({ name: 'OrderDetail' })

const route = useRoute()
const router = useRouter()
const message = useMessage()
const { t } = useI18n()

const orderId = ref(route.params.id as string)
const orderData = ref<OrderVO>({} as OrderVO)
const storeInfo = ref<StoreVO>({} as StoreVO)
const addressInfo = ref<MemberAddressVO>({} as MemberAddressVO)
const loading = ref(true)
const statusLoading = ref(false)
const operationLogs = ref<OrderOperationLogVO[]>([])
const operationLogsLoading = ref(false)
// 创建用户信息
const createUserInfo = ref<MemberVO>({} as MemberVO)

// 是否修改收车地址
let isEditAddress = ref(false)
const addressSelectorRef = ref()
const openAddressSelector = () => {
  addressSelectorRef.value.open()
}

// 是否修改车辆信息
let isEditVehicle = ref(false)

// 返回完整地址（计算属性）
const getFullAddress = computed(() => {
  if (addressInfo.value) {
    return addressInfo.value.province + addressInfo.value.city + addressInfo.value.district + addressInfo.value.detailAddress
  } else {
    return false
  }
})

// 完成订单
const handleFinish = () => {
  OrderApi.finishOrder(orderData.value.orderNo).then(res => {
    message.success('完成订单成功')
    getOrderDetail()
  })
}

// 跳转会员详情
const goMemberDetail = (id: number) => {
  router.push(`/rental/member/detail/${id}`)
}

const refundFormRef = ref()
const refundDialogVisible = ref(false)
const refundForm = ref({
  refundAmount: 0,
  refundReason: ''
})
const refundFormRules = ref({
  refundAmount: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    // 自定义校验规则 不能为0和负数 且不能不能超过订单金额
    { validator: (rule, value, callback) => {
      if (value <= 0) {
        callback(new Error('退款金额不能为0和负数'))
      }
      if (value > orderData.value.amount) {
        callback(new Error('退款金额不能超过订单金额'))
      }
      callback()
    }, trigger: 'blur' }
  ]
})
// 订单退款
const handleRefund = () => {
  // 退款金额《= 实际金额  
  refundFormRef.value.validate((valid) => {
    if (valid) {
      OrderApi.refundOrder({
        id: orderData.value.id,
        refundAmount: Number(refundForm.value.refundAmount),
        refundReason: refundForm.value.refundReason
      }).then(res => {
        message.success('退款成功')
        getOrderDetail()
        refundDialogVisible.value = false
      })
    }
  })
}

// 取消订单
const handleCancel = () => {
  ElMessageBox.confirm('确定要取消订单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    OrderApi.cancelOrder(orderData.value.id)
    message.success('取消订单成功')
    getOrderDetail()
  })
}

// 获取订单详情
const getOrderDetail = async () => {
  loading.value = true
  try {
    orderData.value = await OrderApi.getOrder(Number(orderId.value))
    // 获取门店信息
    if (orderData.value.storeId) {
      storeInfo.value = await StoreApi.getStore(orderData.value.storeId)
    }
    // 获取地址信息
    if (orderData.value.addressId) {
      addressInfo.value = await MemberAddressApi.getMemberAddress(orderData.value.addressId)
    }
    // 获取创建用户信息
    if (orderData.value.userId) {
      createUserInfo.value = await MemberApi.getMember(orderData.value.userId)
    }
    // console.log('订单状态', DICT_TYPE.TRADE_ORDER_STATUS, orderData.value);
  } catch (error) {
    message.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

// 获取操作人类型标签类型
const getOperationUserTypeTag = (type: string) => {
  const typeMap: Record<string, 'success' | 'info' | 'warning' | 'danger' | 'primary'> = {
    'user': 'success',
    'system': 'info',
    'admin': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取操作人类型文本
const getOperationUserTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'user': '用户',
    'system': '系统',
    'admin': '管理员'
  }
  return typeMap[type] || '未知'
}

const formatDate = (dateValue: any) => {
  if (!dateValue) return '--'
  const d = new Date(dateValue);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hour = String(d.getHours()).padStart(2, '0');
  const minute = String(d.getMinutes()).padStart(2, '0');
  const second = String(d.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}

const canChangeStatus = computed(() => {
  return ['pending', 'paid','in_progress'].includes(orderData.value.status)
})

const editVehicleInfo = () => {
  isEditVehicle.value = true
}

// 状态切换
const handleStatusChange = async () => {
  try {
    statusLoading.value = true
    const newStatus = orderData.value.status === 'pending' ? 'paid' : 'completed'
    await OrderApi.updateOrder({
      ...orderData.value,
      status: newStatus
    })
    message.success('状态切换成功')
    await getOrderDetail()
  } catch (error) {
    message.error('状态切换失败')
  } finally {
    statusLoading.value = false
  }
}

// 返回列表
const goBack = () => {
  router.go(-1)
}

const selectAddress = (address: any) => {
  addressInfo.value = address
  orderData.value.addressId = address.id
  updateOrderInfo()
  isEditAddress.value = false
}

const saveVehicleInfo = async () => {
  await OrderApi.updateOrder(orderData.value)
  await getOrderDetail()
  message.success('保存成功')
  isEditVehicle.value = false
}

// 更新订单信息
const updateOrderInfo = async () => {
  await OrderApi.updateOrder(orderData.value)
  await getOrderDetail()
  message.success('更新成功')
}

// 获取订单操作日志
const getOrderOperationLogs = async () => {
  operationLogsLoading.value = true
  try {
    const res = await OrderOperationLogApi.getOrderOperationLogPage({
      orderId: Number(orderId.value),
      pageSize: 100, // 获取足够多的记录
      pageNo: 1
    })
    operationLogs.value = res.list || []
  } catch (error) {
    console.error('获取订单操作日志失败', error)
  } finally {
    operationLogsLoading.value = false
  }
}

// 初始化
onMounted(() => {
  getOrderDetail()
  getOrderOperationLogs()
})
</script>

<style scoped>
.order-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.status-tag {
  font-size: 14px;
  padding: 6px 12px;
}

.header-right {
  display: flex;
  gap: 10px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-weight: 600;
  color: #303133;
}

.vehicle-image-container {
  display: flex;
  align-items: center;
}

.vehicle-image {
  width: 100px;
  height: 60px;
  border-radius: 4px;
  cursor: pointer;
}

.no-image {
  color: #909399;
  font-style: italic;
}

/* 操作日志样式 */
.timeline-content {
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  line-height: 1.6;
}

.user-name {
  font-weight: bold;
  margin: 0 5px;
}

.operation-type {
  margin: 0 5px;
  color: #409EFF;
}

.remark {
  margin-top: 8px;
  color: #606266;
  font-size: 13px;
  padding-left: 5px;
  border-left: 2px solid #dcdfe6;
}

.mr-5 {
  margin-right: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.mx-5 {
  margin-left: 5px;
  margin-right: 5px;
}
</style>