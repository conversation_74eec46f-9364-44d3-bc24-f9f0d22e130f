import request from '@/config/axios'

// 订单操作日志 VO
export interface OrderOperationLogVO {
  id: number // 主键ID
  orderId: number // 订单ID
  orderNo: string // 订单编号
  operationType: string // 操作类型
  operationUserId: number // 操作人ID
  operationUserName: string // 操作人名称
  operationUserType: string // 操作人类型（user-用户, system-系统, admin-管理员）
  operationTime: Date // 操作时间
  previousStatus: string // 操作前状态
  currentStatus: string // 操作后状态
  remark: string // 操作备注
}

// 订单操作日志 API
export const OrderOperationLogApi = {
  // 查询订单操作日志分页
  getOrderOperationLogPage: async (params: any) => {
    return await request.get({ url: `/rental/order-operation-log/page`, params })
  },

  // 查询订单操作日志详情
  getOrderOperationLog: async (id: number) => {
    return await request.get({ url: `/rental/order-operation-log/get?id=` + id })
  },

  // 新增订单操作日志
  createOrderOperationLog: async (data: OrderOperationLogVO) => {
    return await request.post({ url: `/rental/order-operation-log/create`, data })
  },

  // 修改订单操作日志
  updateOrderOperationLog: async (data: OrderOperationLogVO) => {
    return await request.put({ url: `/rental/order-operation-log/update`, data })
  },

  // 删除订单操作日志
  deleteOrderOperationLog: async (id: number) => {
    return await request.delete({ url: `/rental/order-operation-log/delete?id=` + id })
  },
}
