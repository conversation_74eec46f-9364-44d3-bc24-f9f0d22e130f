<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="车辆名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入车辆名称" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="车辆编号" prop="vehicleNo">
        <el-input v-model="queryParams.vehicleNo" placeholder="请输入车辆编号" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="车牌号" prop="licensePlate">
        <el-input v-model="queryParams.licensePlate" placeholder="请输入车牌号" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="车架号" prop="vin">
        <el-input v-model="queryParams.vin" placeholder="请输入车架号" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="门店" prop="storeId">
        <el-select v-model="queryParams.storeId" placeholder="请选择门店" clearable class="!w-240px">
          <el-option v-for="item in storeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="品牌" prop="brandId">
        <el-select v-model="queryParams.brandId" placeholder="请选择品牌" clearable class="!w-240px">
          <el-option v-for="item in brandList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="型号" prop="model">
        <el-input v-model="queryParams.model" placeholder="请输入型号" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.RENTAL_PURCHASE_STATUS)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <el-button type="primary" plain @click="openForm('create')">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" type="index" align="center" width="80" />
      <el-table-column label="车辆图片" align="center" prop="images">
        <template #default="scope">
          <el-image v-if="scope.row.images" :src="scope.row.images" fit="cover" style="width: 50px; height: 50px;" />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="车辆名称" align="center" prop="name" width="100"/>
      <el-table-column label="车辆编号" align="center" prop="vehicleNo" width="200"/>
      <el-table-column label="车牌号" align="center" prop="licensePlate" width="150"/>
      <el-table-column label="车架号" align="center" prop="vin" width="150"/>
      <el-table-column label="门店" align="center" prop="storeName" width="150"/>
      <el-table-column label="品牌" align="center" prop="brandName" />
      <el-table-column label="型号" align="center" prop="model" width="150"/>
      <el-table-column label="租赁价格" align="center" prop="price" />
      <el-table-column label="押金" align="center" prop="deposit" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.RENTAL_PURCHASE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      <el-table-column label="操作" align="center" min-width="200px" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            编辑
          </el-button>
          <el-button link type="primary" @click="openTransferForm(scope.row.id)">
            划拨
          </el-button>
          <el-button v-if="scope.row.status === 2" link type="primary" @click="openUpForm(scope.row)">
            上架
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <VehicleRentForm ref="formRef" @success="getList" />

  <!-- 表单弹窗：划拨 -->
  <VehicleRentTransferForm ref="transferFormRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { VehicleRentApi, VehicleRentVO } from '@/api/rental/vehiclerent'
import VehicleRentForm from './VehicleRentForm.vue'
import VehicleRentTransferForm from './VehicleRentTransferForm.vue'
import { StoreApi, StoreVO } from '@/api/rental/store'
import { BrandApi, BrandVO } from '@/api/rental/brand'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

/** 租赁车辆 列表 */
defineOptions({ name: 'VehicleRent' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<VehicleRentVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  vehicleNo: undefined,
  licensePlate: undefined,
  vin: undefined,
  storeId: undefined,
  brandId: undefined,
  model: undefined,
  detail: undefined,
  price: undefined,
  images: undefined,
  createTime: [],
  name: undefined,
  status: undefined,
  deposit: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {  
  loading.value = true
  try {
    const data = await VehicleRentApi.getVehicleRentPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 上架车辆操作 */
const openUpForm = (row: VehicleRentVO) => {
  // 直接修改状态
  VehicleRentApi.updateVehicleRent({ ...row, status: 1 })
  message.success(t('common.updateSuccess'))
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 划拨操作 */
const transferFormRef = ref()
const openTransferForm = (id: number) => {
  transferFormRef.value.open(id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await VehicleRentApi.deleteVehicleRent(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await VehicleRentApi.exportVehicleRent(queryParams)
    download.excel(data, '租赁车辆.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}


// 获取所有的门店
const storeList = ref<StoreVO[]>([])
const getStoreList = async () => {
  storeList.value = await StoreApi.getStoreList()
}

// 获取所有品牌
const brandList = ref<BrandVO[]>([])
const getBrandList = async () => {
  brandList.value = await BrandApi.getBrandList()
}

/** 初始化 **/
onMounted(() => {
  getList()
  getStoreList()
  getBrandList()
})
</script>