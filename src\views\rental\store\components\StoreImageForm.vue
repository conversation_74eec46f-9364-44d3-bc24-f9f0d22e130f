<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px">
       <el-table-column label="图片地址" >
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.imageUrl`" :rules="formRules.imageUrl" class="mb-0px!">
            <UploadImg v-model="row.imageUrl" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="排序" min-width="80"> 
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sortOrder`" :rules="formRules.sortOrder" class="mb-0px!">
            <el-input-number v-model="row.sortOrder" :min="0" :max="999" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" >
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>
            <el-icon>
              <Delete />
            </el-icon>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加门店图片</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { StoreApi } from '@/api/rental/store'
import { Delete } from '@element-plus/icons-vue'

interface StoreImage {
  id?: number
  storeId: number | undefined
  imageUrl?: string
  sortOrder?: number
}

const props = defineProps<{
  storeId: number | undefined // 门店ID（逻辑关联 rental_store.id）（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<StoreImage[]>([])
const formRules = reactive({
  storeId: [{ required: true, message: '门店ID不能为空', trigger: 'blur' }],
  imageUrl: [{ required: true, message: '图片地址不能为空', trigger: 'blur' }],
  sortOrder: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.storeId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      formData.value = await StoreApi.getStoreImageListByStoreId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    storeId: undefined as number | undefined,
    imageUrl: undefined,
    sortOrder: undefined,
  }
  row.storeId = props.storeId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({ validate, getData })
</script>