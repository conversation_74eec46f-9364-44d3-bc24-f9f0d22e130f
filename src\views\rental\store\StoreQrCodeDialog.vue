<template>
  <Dialog :title="'门店二维码'" v-model="dialogVisible" width="400px">
    <div class="qrcode-container" v-loading="loading">
      <el-image v-if="qrCodeUrl" :src="qrCodeUrl" class="qrcode-image" />
      <div v-else-if="!loading" class="no-qrcode">暂无二维码</div>
    </div>
    <template #footer>
      <el-button @click="handleDownload" type="primary" :disabled="!qrCodeUrl">下载二维码</el-button>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ref, onBeforeUnmount } from 'vue'
import { StoreApi } from '@/api/rental/store'
import download from '@/utils/download'

defineOptions({ name: 'StoreQrCodeDialog' })

const dialogVisible = ref(false)
const loading = ref(false)
const qrCodeUrl = ref('')
const storeId = ref<number>()

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  storeId.value = id
  await generateQrCode()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 生成二维码 */
const generateQrCode = async () => {
  if (!storeId.value) return
  
  loading.value = true
  try {
    // 调用接口获取二维码
    const blob = await StoreApi.generateStoreQrCode(storeId.value)
    // 将二进制数据转换为URL
    qrCodeUrl.value = URL.createObjectURL(blob)
  } catch (error) {
    console.error('获取二维码失败', error)
    qrCodeUrl.value = ''
  } finally {
    loading.value = false
  }
}

/** 下载二维码 */
const handleDownload = () => {
  if (!qrCodeUrl.value) return
  
  // 使用utils/download中的image方法下载图片
  download.image({ 
    url: qrCodeUrl.value,
    drawWithImageSize: true
  })
}

// 组件销毁时释放URL
onBeforeUnmount(() => {
  if (qrCodeUrl.value) {
    URL.revokeObjectURL(qrCodeUrl.value)
  }
})
</script>

<style scoped>
.qrcode-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.qrcode-image {
  max-width: 100%;
  max-height: 300px;
}

.no-qrcode {
  color: #909399;
  font-size: 14px;
}
</style> 