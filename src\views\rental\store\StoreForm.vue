<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="门店名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入门店名称" />
      </el-form-item>
      <el-form-item label="门店地址" prop="address">
        <el-input v-model="formData.address" placeholder="请输入门店地址" />
      </el-form-item>
      <el-form-item label="经度" prop="longitude">
        <el-input v-model="formData.longitude" placeholder="请输入经度">
          <template #append>
            <!-- <el-button link @click="openBaiduMap">拾取</el-button> -->
            <el-button :icon="Location" @click="openBaiduMap"/>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="纬度" prop="latitude">
        <el-input v-model="formData.latitude" placeholder="请输入纬度">
          <template #append>
            <el-button :icon="Location" @click="openBaiduMap"/>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="营业时间" prop="businessHours">
        <el-input v-model="formData.businessHours" placeholder="请输入营业时间（如 10:00-18:00）" />
      </el-form-item>
      <el-form-item label="到店取车" prop="supportPickup">
        <el-radio-group v-model="formData.supportPickup">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.RENTAL_STORE_SUPPORT_PICKUP)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="送车上门" prop="supportDelivery">
        <el-radio-group v-model="formData.supportDelivery">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.RENTAL_STORE_SUPPORT_DELIVERY)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="formData.phone" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item label="门店状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择门店状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.RENTAL_STORE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="门店图片" name="storeImage">
        <StoreImageForm ref="storeImageFormRef" :store-id="formData.id" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { StoreApi, StoreVO } from '@/api/rental/store'
import  StoreImageForm from './components/StoreImageForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { Location } from '@element-plus/icons-vue'

/** 租车门店 表单 */
defineOptions({ name: 'StoreForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  address: undefined,
  longitude: undefined,
  latitude: undefined,
  businessHours: undefined,
  supportPickup: 1,
  supportDelivery: 1,
  phone: undefined,
  status: 1,
})
const formRules = reactive({
  name: [{ required: true, message: '门店名称不能为空', trigger: 'blur' }],
  address: [{ required: true, message: '门店地址不能为空', trigger: 'blur' }],
  supportPickup: [{ required: true, message: '是否到店取车不能为空', trigger: 'blur' }],
  supportDelivery: [{ required: true, message: '是否送车上门不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '门店状态不能为空', trigger: 'blur' }],
  // 经纬度
  longitude: [{ required: true, message: '经度不能为空', trigger: 'blur' }],
  latitude: [{ required: true, message: '纬度不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开百度地图坐标拾取器 */
const openBaiduMap = () => {
  window.open('https://lbs.baidu.com/maptool/getpoint', '_blank')
}

/** 子表的表单 */
const subTabsName = ref('storeImage')
const storeImageFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await StoreApi.getStore(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await storeImageFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'storeImage'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as StoreVO
    // 拼接子表的数据
    data.storeImages = storeImageFormRef.value.getData()
    if (formType.value === 'create') {
      await StoreApi.createStore(data)
      message.success(t('common.createSuccess'))
    } else {
      await StoreApi.updateStore(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    address: undefined,
    longitude: undefined,
    latitude: undefined,
    businessHours: undefined,
    supportPickup: 1,
    supportDelivery: 1,
    phone: undefined,
    status: 1,
  }
  formRef.value?.resetFields()
}
</script>