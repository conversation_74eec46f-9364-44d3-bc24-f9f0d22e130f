<template>
  <ContentWrap>
    <!-- 标题和操作按钮 -->
    <el-card>
      <template #header>
        <div class="flex justify-between">
          <span>用户详情</span>
          <el-button type="primary" @click="goBack">返回</el-button>
        </div>
      </template>
      
      <!-- 用户信息 -->
      <el-descriptions :column="2" border v-loading="loading">
        <el-descriptions-item label="用户头像">
          <el-image
            v-if="member.avatarUrl"
            :src="member.avatarUrl"
            fit="cover"
            style="width: 100px; height: 100px; border-radius: 50%;"
          />
          <el-image
            v-else
            :src="defaultAvatarRef"
            fit="cover"
            style="width: 100px; height: 100px; border-radius: 50%;"
          />
        </el-descriptions-item>
        <el-descriptions-item label="用户昵称">{{ member.nickname }}</el-descriptions-item>
        <el-descriptions-item label="小程序标识">{{ member.openid }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ member.phoneNumber }}</el-descriptions-item>
        <el-descriptions-item label="性别">
          <dict-tag :type="DICT_TYPE.RENTAL_MEMBER_SEX" :value="member.gender" />
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :type="DICT_TYPE.RENTAL_MEMBER_STATUS" :value="member.status" />
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatTime(member.createTime) }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <!-- 实名认证信息 -->
    <el-card class="mt-4">
      <template #header>
        <div class="flex justify-between">
          <span>实名认证信息</span>
          <el-button v-if="hasIdentity" type="danger" @click="unbindIdentity">解绑</el-button>
        </div>
      </template>
      
      <div v-loading="identityLoading">
        <el-empty v-if="!hasIdentity" description="暂无实名认证信息" />
        <el-descriptions v-else :column="2" border>
          <el-descriptions-item label="真实姓名">{{ identity?.realName }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ identity?.idCardNumber }}</el-descriptions-item>
          <el-descriptions-item label="身份证正面照片">
            <el-image
              v-if="identity?.idCardFrontUrl"
              :src="identity.idCardFrontUrl"
              fit="cover"
              style="width: 200px; height: auto;"
            />
            <span v-else>无</span>
          </el-descriptions-item>
          <el-descriptions-item label="身份证反面照片">
            <el-image
              v-if="identity?.idCardBackUrl"
              :src="identity.idCardBackUrl"
              fit="cover"
              style="width: 200px; height: auto;"
            />
            <span v-else>无</span>
          </el-descriptions-item>
          <el-descriptions-item label="认证状态">
            <el-tag
              :type="identity?.status === 1 ? 'success' : 'danger'"
              >{{ identity?.status === 1 ? '已认证' : '未认证' }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="认证通过时间">
            {{ formatTime(identity?.verifiedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
    
    <!-- 会员地址列表 -->
    <el-card class="mt-4">
      <template #header>
        <div class="flex justify-between">
          <span>会员地址列表</span>
        </div>
      </template>
      
      <el-table v-loading="addressLoading" :data="addressList" :stripe="true" :show-overflow-tooltip="true">
        <el-table-column label="序号" type="index" align="center" width="80" />
        <el-table-column label="联系人" align="center" prop="contactName" />
        <el-table-column label="联系电话" align="center" prop="contactPhone" />
        <el-table-column label="省市区" align="center">
          <template #default="scope">
            {{ scope.row.province }} {{ scope.row.city }} {{ scope.row.district }}
          </template>
        </el-table-column>
        <el-table-column label="详细地址" align="center" prop="detailAddress" />
        <el-table-column label="默认地址" align="center" prop="isDefault">
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.isDefault">是</el-tag>
            <el-tag type="info" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button
              link
              type="danger"
              @click="handleDeleteAddress(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="addressTotal"
        v-model:page="addressQueryParams.pageNo"
        v-model:limit="addressQueryParams.pageSize"
        @pagination="handleAddressPagination"
      />
    </el-card>
  </ContentWrap>
</template>
<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { MemberIdentityApi, MemberIdentityVO } from '@/api/rental/memberidentity'
import { MemberApi, MemberVO } from '@/api/rental/member'
import { MemberAddressApi, MemberAddressVO } from '@/api/rental/memberaddress'
import { DICT_TYPE } from '@/utils/dict'
import defaultAvatar from '@/assets/imgs/default-avatar.png'
import { computed, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
import dayjs from 'dayjs'

const { t } = useI18n() // 国际化
const { back } = useRouter() // 路由
const route = useRoute() // 当前路由
const message = useMessage() // 消息弹窗

// 获取路由中的ID参数
const id = ref(parseInt(route.params.id as string))
// 用户详情数据
const member = ref<MemberVO>({} as MemberVO)
// 加载状态
const loading = ref(false)
// 默认头像
const defaultAvatarRef = ref(defaultAvatar)

// 实名认证信息
const identity = ref<MemberIdentityVO | null>(null)
// 实名认证加载状态
const identityLoading = ref(false)
// 是否有实名认证信息
const hasIdentity = computed(() => identity.value !== null)

// 会员地址列表
const addressList = ref<MemberAddressVO[]>([])
// 会员地址加载状态
const addressLoading = ref(false)
// 会员地址总数
const addressTotal = ref(0)
// 会员地址查询参数
const addressQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  memberId: 0
})

/** 格式化日期 */
const formatTime = (time: string | undefined, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  if (!time) return ''
  return dayjs(time).format(format)
}

/** 获取用户详情 */
const getMemberDetail = async () => {
  loading.value = true
  try {
    const data = await MemberApi.getMember(id.value)
    member.value = data
  } finally {
    loading.value = false
  }
}

/** 获取实名认证信息 */
const getMemberIdentity = async () => {
  identityLoading.value = true
  try {
    const data = await MemberIdentityApi.getMemberIdentityByMemberId(id.value)
    identity.value = data
  } catch (error) {
    identity.value = null
  } finally {
    identityLoading.value = false
  }
}

/** 获取会员地址列表 */
const getMemberAddressList = async () => {
  addressLoading.value = true
  try {
    addressQueryParams.memberId = id.value
    const data = await MemberAddressApi.getMemberAddressPage(addressQueryParams)
    addressList.value = data.list
    addressTotal.value = data.total
  } finally {
    addressLoading.value = false
  }
}

/** 解绑实名认证 */
const unbindIdentity = async () => {
  if (!identity.value) return
  
  try {
    // 删除确认
    await message.delConfirm('是否解绑所选中数据？')
    // 发起删除
    await MemberIdentityApi.deleteMemberIdentity(identity.value.id)
    message.success(t('common.delSuccess'))
    // 刷新数据
    identity.value = null
  } catch {}
}

/** 删除会员地址 */
const handleDeleteAddress = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MemberAddressApi.deleteMemberAddress(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getMemberAddressList()
  } catch {}
}

/** 分页操作 */
const handleAddressPagination = () => {
  getMemberAddressList()
}

/** 返回列表页面 */
const goBack = () => {
  back()
}

/** 初始化 */
onMounted(() => {
  getMemberDetail()
  getMemberIdentity()
  getMemberAddressList()
})
</script>