<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="订单编号" prop="orderNo">
        <el-input v-model="queryParams.orderNo" placeholder="请输入订单编号" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="订单类型" prop="orderType">
        <el-select v-model="queryParams.orderType" placeholder="请选择订单类型" clearable class="!w-240px">
          <el-option label="租车" value="rent" />
          <el-option label="购车" value="buy" />
        </el-select>
      </el-form-item>
      <el-form-item label="门店" prop="storeId">
        <el-select v-model="queryParams.storeId" placeholder="请选择门店" clearable class="!w-240px">
          <el-option v-for="item in storeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="押金类型" prop="depositType">
        <el-select v-model="queryParams.depositType" placeholder="请选择押金类型" clearable class="!w-240px">
          <el-option label="支付宝免押" value="alipay_free" />
          <el-option label="交押金" value="normal" />
        </el-select>
      </el-form-item>
      <el-form-item label="租赁周期" prop="rentMode">
        <el-select v-model="queryParams.rentMode" placeholder="请选择租赁周期" clearable class="!w-240px">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.RENTAL_CUSTOM_PRICE_MODEL)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择订单状态" clearable class="!w-240px">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.VEHICLE_ORDER_STATUS)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column type="index" label="序号" width="55" />
      <el-table-column label="订单编号" align="center" prop="orderNo" width="180" />
      <el-table-column label="车辆图片" align="center" prop="vehicleImage" width="180">
        <template #default="scope">
          <el-image
            v-if="scope.row.vehicleImage"
            :src="scope.row.vehicleImage"
            fit="cover"
            style="width: 50px; height: 50px;"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="门店名称" align="center" prop="storeName" width="180" />
      <el-table-column label="车辆名称" align="center" prop="vehicleName" width="180" />
      <el-table-column label="订单类型" align="center" prop="orderType">
        <template #default="scope">
          <el-tag v-if="scope.row.orderType === 'rent'" type="success">租车</el-tag>
          <el-tag v-else-if="scope.row.orderType === 'buy'" type="danger">购车</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="车架号" align="center" prop="vehicleVin" width="180" />
      <el-table-column label="车牌" align="center" prop="licensePlate" width="180" />
      <el-table-column label="用户名" align="center" prop="userName" width="100" />
      <el-table-column label="用户手机" align="center" prop="userPhone" width="180" />
      <el-table-column label="消费方式" align="center" prop="rentPaymentMode">
        <template #default="scope">
          <el-tag v-if="scope.row.rentPaymentMode === 'prepay'" type="success">先付后用</el-tag>
          <el-tag v-else-if="scope.row.rentPaymentMode === 'postpay'" type="danger">先用后付</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="订单金额" align="center" prop="amount" width="180" />
      <el-table-column label="支付宝交易凭证号" align="center" prop="alipayTradeNo" width="180" />
      <el-table-column label="订单状态" align="center" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VEHICLE_ORDER_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="取车方式" align="center" prop="pickupMethod" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.pickupMethod === 'to_store'" type="success">到店取车</el-tag>
          <el-tag v-else-if="scope.row.pickupMethod === 'deliver'" type="danger">送车上门</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="押金金额" align="center" prop="depositAmount" width="150" />
      <el-table-column label="押金类型" align="center" prop="depositType" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.depositType === 'alipay_free'" type="success">支付宝免押</el-tag>
          <el-tag v-else-if="scope.row.depositType === 'normal'" type="danger">交押金</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="租赁周期" align="center" prop="rentMode" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.RENTAL_CUSTOM_PRICE_MODEL" :value="scope.row.rentMode" />
        </template>
      </el-table-column>
      <el-table-column label="租赁开始时间" align="center" prop="rentStartTime" :formatter="dateFormatter" width="180px" />
      <el-table-column label="租赁时长" align="center" prop="rentDuration" />
      <el-table-column label="创建时间" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="goToDetail(scope.row.id)">
            详情
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { OrderApi, OrderVO } from '@/api/rental/order'
import { StoreApi, StoreVO } from '@/api/rental/store'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
/** 租车/购车统一订单 列表 */
defineOptions({ name: 'Order' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const list = ref<OrderVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderNo: undefined,
  userId: undefined,
  orderType: undefined,
  storeId: undefined,
  vehicleId: undefined,
  depositType: undefined,
  depositAmount: undefined,
  rentMode: undefined,
  rentPaymentMode: undefined,
  rentStartTime: [],
  rentDuration: undefined,
  purchasePlan: undefined,
  installmentFee: undefined,
  amount: undefined,
  payType: undefined,
  alipayTradeNo: undefined,
  status: undefined,
  createTime: [],
  isDeleted: undefined,
  storeName: undefined,
  vehicleName: undefined,
  vehicleVin: undefined,
  licensePlate: undefined,
  userName: undefined,
  userPhone: undefined,
  pickupMethod: undefined,
  addressId: undefined,
})
const queryFormRef = ref() // 搜索的表单

// 获取所有的门店
const storeList = ref<StoreVO[]>([])
const getStoreList = async () => {
  storeList.value = await StoreApi.getStoreList()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderApi.getOrderPage(queryParams)
    list.value = data.list
    console.log('订单列表',list.value);
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 跳转到详情页面 */
const goToDetail = (id: number) => {
  router.push(`/rental/order/detail/${id}`)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await OrderApi.deleteOrder(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 初始化 **/
onMounted(() => {
  getStoreList()
  getList()
})
</script>