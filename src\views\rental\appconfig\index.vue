<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="配置名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入配置名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="配置类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择配置类型"
          clearable
          class="!w-240px"
        >
           <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.RENTAL_APP_CONFIG_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
        </el-select>
      </el-form-item>
      <el-form-item label="配置编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入配置编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['rental:app-config:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" type="index" align="center" width="80" />
      <el-table-column label="配置名称" align="center" prop="name" />
      <el-table-column label="配置图标" align="center" prop="icon" >
        <template #default="scope">
          <el-image
            v-if="scope.row.icon"
            :src="scope.row.icon"
            fit="cover"
            style="width: 50px; height: 50px;"
          />
        </template>
      </el-table-column>
      <el-table-column label="配置类型" align="center" prop="type">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.RENTAL_APP_CONFIG_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="配置编码" align="center" prop="code" />
      <el-table-column label="内容" align="center" prop="content">
        <template #default="scope">
          <el-button link type="primary" @click="handleViewContent(scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <AppConfigForm ref="formRef" @success="getList" />

  <!-- 内容查看对话框 -->
  <Dialog v-model="contentDialogVisible" :title="currentViewName + ' - 内容详情'">
    <div class="content-preview" v-html="currentViewContent"></div>
  </Dialog>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { AppConfigApi, AppConfigVO } from '@/api/rental/appconfig'
import AppConfigForm from './AppConfigForm.vue'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { Dialog } from '@/components/Dialog'

/** APP端配置 列表 */
defineOptions({ name: 'AppConfig' })

const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const list = ref<AppConfigVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  type: undefined,
  code: undefined,
  content: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载

// 内容查看对话框相关变量
const contentDialogVisible = ref(false) // 内容查看对话框是否可见
const currentViewContent = ref('') // 当前查看的内容
const currentViewName = ref('') // 当前查看的配置名称

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AppConfigApi.getAppConfigPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AppConfigApi.deleteAppConfig(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AppConfigApi.exportAppConfig(queryParams)
    download.excel(data, 'APP端配置.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 内容查看操作 */
const handleViewContent = (row: AppConfigVO) => {
  currentViewContent.value = row.content
  currentViewName.value = row.name
  contentDialogVisible.value = true
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.content-preview {
  max-height: 500px;
  overflow-y: auto;
  padding: 10px;

  :deep(img) {
    max-width: 100%;
  }

  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    
    th, td {
      border: 1px solid #dcdfe6;
      padding: 8px;
    }
  }
}
</style>